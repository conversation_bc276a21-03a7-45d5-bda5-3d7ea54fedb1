[2025-09-08 00:14:42.769 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:42.782 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:42.790 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.115 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.122 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:43.133 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-08 00:14:43.184 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-08 00:14:43.249 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:43.250 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:43.252 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.301 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.304 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:43.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-08 00:14:43.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config` ORDER BY `map_id` ASC 
[2025-09-08 00:14:43.373 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:43.374 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:43.376 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.425 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:43.428 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:43.431 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-08 00:14:43.532 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config` ORDER BY `monster_no` ASC 
[2025-09-08 00:14:43.641 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:43.890 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:44.018 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:44.067 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:44.079 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:44.088 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `map_monster`  
[2025-09-08 00:14:44.148 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `map_monster`  
[2025-09-08 00:14:44.151 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-08 00:14:44.197 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`monster_id`,`monster_name`,`growth`,`element`,`max_level`,`min_level`,`max_drop`,`exp`,`hp`,`mp`,`max_hp`,`max_mp`,`atk`,`def`,`dodge`,`spd`,`hit`,`deepen`,`offset`,`vamp`,`vamp_mp`,`skill_list`,`drop_items` FROM `map_monster`    ORDER BY `map_id` ASC,`monster_id` ASC LIMIT 0,10
[2025-09-08 00:14:44.201 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-08 00:14:44.247 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`map_id`,`map_name`,`map_desc`,`map_size`,`map_type`,`atlast_name`,`background`,`bgm`,`bgm_loop`,`bgm_volume`,`bgm_play`,`bgm_mute`,`bgm_pause`,`ico`,`type` FROM `map_config`  WHERE  (`map_id` IN (100,101,102,103,104,105,106,107,108))  
[2025-09-08 00:14:54.751 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:54.937 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:54.942 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:55.010 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:55.063 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:55.121 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:14:55.123 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:14:55.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:55.172 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:14:55.179 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:14:55.197 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  
[2025-09-08 00:14:55.244 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  
[2025-09-08 00:14:55.246 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-08 00:14:55.292 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`    ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-08 00:15:19.768 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:15:19.773 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:15:19.775 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:19.821 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:19.840 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:15:19.850 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  WHERE ( `monster_no` = @monster_no0 )  | 参数: [@monster_no0=101]
[2025-09-08 00:15:19.898 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  WHERE ( `monster_no` = @monster_no0 ) 
[2025-09-08 00:15:19.899 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`   WHERE ( `monster_no` = @monster_no0 )  ORDER BY `monster_no` ASC LIMIT 0,10 | 参数: [@monster_no0=101]
[2025-09-08 00:15:19.944 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`   WHERE ( `monster_no` = @monster_no0 )  ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-08 00:15:29.301 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:15:29.305 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:15:29.306 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:29.353 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:29.355 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:15:29.356 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT COUNT(1) FROM `monster_config`  WHERE ( `monster_no` = @monster_no0 )  | 参数: [@monster_no0=111]
[2025-09-08 00:15:29.402 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT COUNT(1) FROM `monster_config`  WHERE ( `monster_no` = @monster_no0 ) 
[2025-09-08 00:15:29.402 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`   WHERE ( `monster_no` = @monster_no0 )  ORDER BY `monster_no` ASC LIMIT 0,10 | 参数: [@monster_no0=111]
[2025-09-08 00:15:29.448 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `id`,`monster_no`,`skill`,`name`,`attribute` FROM `monster_config`   WHERE ( `monster_no` = @monster_no0 )  ORDER BY `monster_no` ASC LIMIT 0,10
[2025-09-08 00:15:41.662 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:15:41.666 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:15:41.669 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:41.715 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:41.717 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:15:41.721 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:15:41.769 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:15:41.774 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/8 0:15:41]
[2025-09-08 00:15:41.852 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-08 00:15:41.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-08 00:15:41.906 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-08 00:15:41.910 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=击杀 小狸猫10个, @createdAt0=2025/9/8 0:15:41, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=击杀金波姆10个, @createdAt1=2025/9/8 0:15:41]
[2025-09-08 00:15:41.984 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-08 00:15:41.989 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:15:42.037 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:15:42.039 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:15:42.086 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.099 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:15:42.105 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:15:42.106 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:42.161 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:15:42.163 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:15:42.166 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:15:42.213 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:15:42.214 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:15:42.263 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:15:42.278 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-08 00:15:42.331 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.335 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:15:42.394 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.396 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-08 00:15:42.443 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-08 00:15:42.492 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.495 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-08 00:15:42.544 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:15:42.549 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-08 00:15:42.595 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:47.722 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:16:48.051 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:16:48.090 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:16:48.169 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:16:48.240 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:16:48.262 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:16:48.368 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:16:48.377 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/8 0:16:48]
[2025-09-08 00:16:48.465 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-08 00:16:48.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-08 00:16:48.529 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-08 00:16:48.532 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=击杀小狸猫10个, @createdAt0=2025/9/8 0:16:48, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=击杀金波姆10个, @createdAt1=2025/9/8 0:16:48]
[2025-09-08 00:16:48.584 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-08 00:16:48.587 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:16:48.635 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:16:48.638 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:16:48.687 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:48.709 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:16:48.711 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:16:48.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:16:48.760 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:16:48.763 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:16:48.768 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:16:48.816 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:16:48.819 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:16:48.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:16:48.871 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-08 00:16:48.918 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:48.922 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:16:49.007 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:49.011 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-08 00:16:49.060 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:49.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-08 00:16:49.112 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:49.117 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-08 00:16:49.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:16:49.172 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-08 00:16:49.220 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:26.161 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:17:26.163 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:17:26.164 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:26.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:26.213 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:17:26.216 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:17:26.265 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:17:26.272 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/8 0:17:26]
[2025-09-08 00:17:26.362 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-08 00:17:26.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-08 00:17:26.424 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-08 00:17:26.429 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=<span>击杀小狸猫10个</span></br>, @createdAt0=2025/9/8 0:17:26, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=<span>击杀金波姆10个</span></br>, @createdAt1=2025/9/8 0:17:26]
[2025-09-08 00:17:26.494 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-08 00:17:26.497 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:17:26.554 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:17:26.556 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:17:26.604 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:26.613 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:17:26.622 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:17:26.625 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:26.674 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:26.677 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:17:26.680 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:17:26.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:17:26.822 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:17:26.870 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:17:26.876 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-08 00:17:26.928 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:26.996 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:17:27.058 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:27.061 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-08 00:17:27.107 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:27.109 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-08 00:17:27.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:27.159 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-08 00:17:27.205 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:27.209 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-08 00:17:27.260 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:17:58.231 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:17:59.229 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:17:59.749 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:59.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:17:59.796 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:17:59.799 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:18:00.240 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:18:00.361 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_002, @task_name=新手地图击杀任务, @task_description=亲爱的口袋勇士！我这里有一些精美礼品送给你们哦，但是也不能不劳而获，来帮助自然女神做几件事情吧，完成后这些精美礼品就属于你的啦..., @task_type=1, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"金币","Id":"gold","Amount":50000},{"Type":"经验","Id":"exp","Amount":10000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/9/8 0:18:00]
[2025-09-08 00:18:00.602 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-09-08 00:18:00.671 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_002]
[2025-09-08 00:18:00.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-09-08 00:18:00.873 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_002, @objectiveType0=KILL_MONSTER, @targetId0=111, @targetAmount0=10, @objectiveOrder0=1, @objectiveDescription0=<span>击杀小狸猫10个</span></br>, @createdAt0=2025/9/8 0:18:00, @taskId1=TASK_002, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=<span>击杀金波姆10个</span></br>, @createdAt1=2025/9/8 0:18:00]
[2025-09-08 00:18:00.932 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-09-08 00:18:00.973 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-09-08 00:18:01.064 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-09-08 00:18:01.069 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:18:01.117 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:01.142 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-09-08 00:18:01.144 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-09-08 00:18:01.150 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:18:01.199 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-09-08 00:18:01.221 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-09-08 00:18:01.270 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:18:01.342 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-09-08 00:18:01.713 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:18:02.136 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-09-08 00:18:02.192 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-09-08 00:18:02.296 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:02.304 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-09-08 00:18:02.404 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:02.463 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-09-08 00:18:02.575 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:02.616 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-09-08 00:18:02.760 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:02.803 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-09-08 00:18:02.911 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-09-08 00:18:02.919 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-09-08 00:18:02.971 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
